'use client';
import React, { useRef, useState, useCallback, useEffect } from 'react';
import { motion, useMotionValue, useSpring, SpringOptions } from 'framer-motion';
import { cn } from '@/lib/utils';

type CursorSpotlightProps = {
  className?: string;
  size?: number;
  springOptions?: SpringOptions;
};

export function CursorSpotlight({
  className,
  size = 180,
  springOptions = { damping: 25, stiffness: 150, mass: 0.1 },
}: CursorSpotlightProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  const springX = useSpring(mouseX, springOptions);
  const springY = useSpring(mouseY, springOptions);

  // Prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Update both motion values and state
    mouseX.set(x);
    mouseY.set(y);
    setMousePosition({ x, y });
  }, [mouseX, mouseY]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  // Don't render on server to prevent hydration mismatch
  if (!isMounted) return null;

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
      style={{ zIndex: 15 }}
    >
      <div
        className="absolute inset-0 pointer-events-auto"
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />

      {/* Focused Point Light Effect */}
      <motion.div
        className={cn(
          'pointer-events-none absolute rounded-full',
          'blur-md transition-opacity duration-300',
          isHovered ? 'opacity-100' : 'opacity-0',
          className
        )}
        style={{
          width: size,
          height: size,
          x: springX,
          y: springY,
          translateX: -size / 2,
          translateY: -size / 2,
          background: 'radial-gradient(circle at center, rgba(251, 191, 36, 0.9) 0%, rgba(249, 115, 22, 0.7) 30%, rgba(239, 68, 68, 0.4) 50%, transparent 60%)',
        }}
      />

      {/* Inner concentrated core for more defined center */}
      <motion.div
        className={cn(
          'pointer-events-none absolute rounded-full',
          'blur-sm transition-opacity duration-300',
          isHovered ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          width: size * 0.4,
          height: size * 0.4,
          x: springX,
          y: springY,
          translateX: -(size * 0.4) / 2,
          translateY: -(size * 0.4) / 2,
          background: 'radial-gradient(circle at center, rgba(251, 191, 36, 0.95) 0%, rgba(249, 115, 22, 0.8) 40%, transparent 70%)',
        }}
      />
    </div>
  );
}
