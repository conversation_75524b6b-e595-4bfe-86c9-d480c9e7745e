'use client';
import React, { useRef, useState, useCallback, useEffect } from 'react';
import { motion, useMotionValue, useSpring, SpringOptions } from 'framer-motion';
import { cn } from '@/lib/utils';

type CursorSpotlightProps = {
  className?: string;
  size?: number;
  springOptions?: SpringOptions;
};

export function CursorSpotlight({
  className,
  size = 400,
  springOptions = { damping: 25, stiffness: 150, mass: 0.1 },
}: CursorSpotlightProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  const springX = useSpring(mouseX, springOptions);
  const springY = useSpring(mouseY, springOptions);

  // Prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('CursorSpotlight mounted');
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Update both motion values and state
    mouseX.set(x);
    mouseY.set(y);
    setMousePosition({ x, y });

    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('Mouse move:', { x, y, isHovered, rect });
    }
  }, [mouseX, mouseY, isHovered]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    if (process.env.NODE_ENV === 'development') {
      console.log('Mouse entered cursor spotlight area');
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    if (process.env.NODE_ENV === 'development') {
      console.log('Mouse left cursor spotlight area');
    }
  }, []);

  // Don't render on server to prevent hydration mismatch
  if (!isMounted) return null;

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
      style={{ zIndex: 15 }}
    >
      <div
        className="absolute inset-0 pointer-events-auto"
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />

      {/* Debug indicator - only in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 left-4 text-white text-xs bg-black/80 p-2 rounded" style={{ zIndex: 100 }}>
          Cursor Spotlight: {isHovered ? 'Active' : 'Inactive'} | Pos: {mousePosition.x.toFixed(0)}, {mousePosition.y.toFixed(0)}
        </div>
      )}

      {/* Framer Motion version */}
      <motion.div
        className={cn(
          'pointer-events-none absolute rounded-full',
          'blur-3xl transition-opacity duration-300',
          // Temporarily make it always visible for debugging
          process.env.NODE_ENV === 'development' ? 'opacity-60' : (isHovered ? 'opacity-100' : 'opacity-0'),
          className
        )}
        style={{
          width: size,
          height: size,
          x: springX,
          y: springY,
          translateX: -size / 2,
          translateY: -size / 2,
          background: 'radial-gradient(circle at center, rgba(251, 191, 36, 0.6) 0%, rgba(249, 115, 22, 0.4) 40%, rgba(239, 68, 68, 0.2) 70%, transparent 100%)',
        }}
      />

      {/* Fallback CSS version for debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div
          className={cn(
            'pointer-events-none absolute rounded-full',
            'blur-xl transition-all duration-300',
            'opacity-30',
            'border-2 border-red-500'
          )}
          style={{
            width: size / 2,
            height: size / 2,
            left: mousePosition.x - size / 4,
            top: mousePosition.y - size / 4,
            background: 'radial-gradient(circle at center, rgba(255, 0, 0, 0.3) 0%, transparent 70%)',
          }}
        />
      )}
    </div>
  );
}
