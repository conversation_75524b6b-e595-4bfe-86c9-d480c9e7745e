'use client'

import { CursorSpotlight } from '@/components/ui/cursor-spotlight'

export default function TestCursorPage() {
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="relative w-[800px] h-[600px] bg-gray-900 rounded-lg border border-gray-700">
        <CursorSpotlight
          size={300}
          springOptions={{ bounce: 0, damping: 20, stiffness: 120 }}
        />
        
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-center">
            <h1 className="text-4xl font-bold mb-4">Cursor Spotlight Test</h1>
            <p className="text-gray-300">Move your mouse around this area to see the cursor spotlight effect</p>
          </div>
        </div>
      </div>
    </div>
  )
}
